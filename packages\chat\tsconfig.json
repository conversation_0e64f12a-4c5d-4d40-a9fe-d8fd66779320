{"compilerOptions": {"target": "es2017", "lib": ["dom", "dom.iterable", "es6"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": false, "esModuleInterop": true, "module": "commonjs", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "declaration": true, "outDir": "dist", "rootDir": "src"}, "include": ["src"], "exclude": ["node_modules", "dist"]}