{"name": "@repo/database", "version": "1.0.0", "private": true, "type": "module", "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": "./dist/index.js", "./schemas": "./dist/schemas/index.js"}, "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint --config eslint.config.js src/", "type-check": "tsc --noEmit", "clean": "rd /s /q dist", "test": "jest", "db:generate": "prisma generate", "db:migrate:dev": "prisma migrate dev", "db:studio": "prisma studio"}, "dependencies": {"@supabase/supabase-js": "^2.55.0", "zod": "^3.22.0"}, "devDependencies": {"@types/jest": "^29.0.0", "@types/pg": "^8.10.0", "eslint": "^8.0.0", "jest": "^29.0.0", "ts-jest": "^29.4.0", "typescript": "^5.0.0"}}