{"compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM"], "outDir": "./dist", "rootDir": "./src", "declaration": true, "declarationMap": true, "sourceMap": true, "noEmit": false, "module": "es2020", "moduleResolution": "bundler", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "skipLibCheck": true}, "include": ["src/**/*"], "exclude": ["dist", "node_modules", "__tests__", "examples"]}