
name: CI/CD

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  ci:
    name: Continuous Integration
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: "18"
          cache: "npm"

      - name: Install Dependencies
        run: npm install

      - name: Check Formatting
        run: npm run format:check

      - name: Lint
        run: npm run lint

      - name: Type Check
        run: npm run type-check

      - name: Test
        run: npm run test

      - name: Build
        run: npm run build

  # cd:
  #   name: Continuous Deployment
  #   runs-on: ubuntu-latest
  #   needs: ci
  #   if: github.ref == 'refs/heads/main'

  #   steps:
  #     - name: Checkout
  #       uses: actions/checkout@v3

  #     - name: Setup Node.js
  #       uses: actions/setup-node@v3
  #       with:
  #         node-version: "18"
  #         cache: "npm"

  #     - name: Install Dependencies
  #       run: npm install

  #     - name: Build
  #       run: npm run build

  #     - name: Deploy to Vercel
  #       uses: amondnet/vercel-action@v20
  #       with:
  #         vercel-token: ${{ secrets.VERCEL_TOKEN }}
  #         vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
  #         vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
