{"name": "@repo/convex", "version": "1.0.0", "description": "Convex database and real-time functions for Content Boost platform", "type": "module", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./nextjs": {"types": "./dist/nextjs.d.ts", "default": "./dist/nextjs.js"}}, "scripts": {"build": "tsc", "dev": "convex dev", "deploy": "convex deploy", "clean": "rm -rf dist .convex", "type-check": "tsc --noEmit", "lint": "eslint --config ../../eslint.config.js ."}, "dependencies": {"convex": "^1.16.5"}, "devDependencies": {"@types/node": "^20.0.0", "typescript": "^5.0.0"}, "peerDependencies": {"react": "^18.0.0", "next": "^14.0.0"}}