{"name": "@repo/cache", "version": "1.0.0", "private": true, "type": "module", "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": "./src/index.ts"}, "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint --config eslint.config.js src/", "type-check": "tsc --noEmit", "clean": "rd /s /q dist", "test": "jest"}, "dependencies": {"ioredis": "^5.3.0"}, "devDependencies": {"@types/node": "^24.1.0", "typescript": "^5.0.0", "eslint": "^9.32.0", "jest": "^30.0.5", "@types/jest": "^30.0.0", "ts-jest": "^29.0.0", "redis-memory-server": "^0.12.1"}}