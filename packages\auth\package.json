{"name": "@repo/auth", "version": "1.0.0", "private": true, "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": "./dist/index.js", "./config": "./src/config.ts", "./server-only": "./src/server-only.ts", "./client": "./src/client.ts", "./social": "./src/social/index.ts", "./types": "./src/types.ts", "./provider": "./src/provider.tsx"}, "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint --config eslint.config.js src/", "type-check": "tsc --noEmit", "clean": "rd /s /q dist", "test": "jest"}, "dependencies": {"@repo/database": "*", "@supabase/supabase-js": "^2.55.0", "better-auth": "^1.3.4", "react": "^19.1.1", "zod": "^4.0.10"}, "devDependencies": {"@types/jest": "^30.0.0", "@types/node": "^24.1.0", "eslint": "^9.32.0", "jest": "^30.0.5", "ts-jest": "^29.0.0", "typescript": "^5.0.0"}}