'use client';

import React from 'react';

interface PromoterDiscoveryProps {
  creatorId: string;
  supabaseUrl: string;
  supabaseKey: string;
}

export function PromoterDiscovery({
  creatorId,
  supabaseUrl,
  supabaseKey,
}: PromoterDiscoveryProps) {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Promoter Discovery</h2>
          <p className="text-gray-600">Find and connect with promoters</p>
        </div>
      </div>
      <div className="bg-white p-6 rounded-lg border">
        <p className="text-center text-gray-500">
          Promoter Discovery - Component placeholder
        </p>
        <p className="text-sm text-gray-400 text-center">
          Creator: {creatorId}
        </p>
      </div>
    </div>
  );
}
