{"name": "@repo/config", "version": "1.0.0", "private": true, "type": "module", "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": "./src/index.ts", "./supabase": "./src/supabase.ts"}, "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint --config eslint.config.js src/", "type-check": "tsc --noEmit", "clean": "rd /s /q dist", "test": "jest"}, "dependencies": {"zod": "^4.0.10"}, "devDependencies": {"typescript": "^5.0.0", "eslint": "^9.32.0", "jest": "^30.0.5", "@types/jest": "^30.0.0"}}