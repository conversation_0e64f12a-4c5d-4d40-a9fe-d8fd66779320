// UI Components Package
export * from './components/alert';
export * from './components/badge';
export * from './components/button';
export * from './components/card';
export * from './components/dialog';
export * from './components/dropdown-menu';
export * from './components/input';
export * from './components/label';
export * from './components/progress';
export * from './components/radio-group';
export * from './components/select';
export * from './components/switch';
export * from './components/tabs';
export * from './components/textarea';

// New components
export * from './components/Chat';
export * from './components/ab-testing-dashboard';
export * from './components/avatar';
export * from './components/badge-card';
export * from './components/badge-list';
export * from './components/campaign-templates';
export * from './components/gamification-dashboard';
export * from './components/promoter-discovery';
export * from './components/accordion';
export * from './components/scroll-area';

// Re-export common utilities
export { cn } from './lib/utils';

// Types
export type { ComponentProps } from './types';
