{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["**/.env.*local"], "tasks": {"build": {"dependsOn": ["^build"], "outputs": [".next/**", "!.next/cache/**", "dist/**"], "env": ["NODE_ENV", "DATABASE_URL"]}, "dev": {"cache": false, "persistent": true, "env": ["NODE_ENV", "DATABASE_URL"]}, "lint": {"dependsOn": ["^lint"], "outputs": []}, "type-check": {"dependsOn": ["^type-check"], "outputs": []}, "test": {"dependsOn": ["^build"], "outputs": ["coverage/**"], "inputs": ["src/**/*.tsx", "src/**/*.ts", "__tests__/**/*.ts", "__tests__/**/*.tsx"]}, "clean": {"cache": false, "outputs": []}}, "globalEnv": ["NODE_ENV", "DATABASE_URL", "REDIS_URL", "NEXTAUTH_SECRET", "NEXTAUTH_URL", "TIKTOK_CLIENT_ID", "TIKTOK_CLIENT_SECRET", "INSTAGRAM_CLIENT_ID", "INSTAGRAM_CLIENT_SECRET", "CONVEX_URL", "NEXT_PUBLIC_CONVEX_URL", "CONVEX_DEPLOYMENT"]}