{"config": {"configFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\content-boost\\apps\\dashboard-app\\playwright.config.ts", "rootDir": "C:/Users/<USER>/Documents/augment-projects/content-boost/apps/dashboard-app/__tests__/e2e", "forbidOnly": false, "fullyParallel": false, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "../../reports/e2e"}], ["json", {"outputFile": "../../reports/e2e/results.json"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "C:/Users/<USER>/Documents/augment-projects/content-boost/apps/dashboard-app/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "chromium", "name": "chromium", "testDir": "C:/Users/<USER>/Documents/augment-projects/content-boost/apps/dashboard-app/__tests__/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/Documents/augment-projects/content-boost/apps/dashboard-app/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "firefox", "name": "firefox", "testDir": "C:/Users/<USER>/Documents/augment-projects/content-boost/apps/dashboard-app/__tests__/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/Documents/augment-projects/content-boost/apps/dashboard-app/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "webkit", "name": "webkit", "testDir": "C:/Users/<USER>/Documents/augment-projects/content-boost/apps/dashboard-app/__tests__/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.54.2", "workers": 8, "webServer": {"command": "npm run dev", "port": 3002, "reuseExistingServer": true, "timeout": 120000, "cwd": "./"}}, "suites": [], "errors": [{"message": "Error: No tests found.\nMake sure that arguments are regular expressions matching test files.\nYou may need to escape symbols like \"$\" or \"*\" and quote the arguments.", "stack": "Error: No tests found.\nMake sure that arguments are regular expressions matching test files.\nYou may need to escape symbols like \"$\" or \"*\" and quote the arguments."}], "stats": {"startTime": "2025-08-17T00:55:47.777Z", "duration": 14.275000000000091, "expected": 0, "skipped": 0, "unexpected": 0, "flaky": 0}}