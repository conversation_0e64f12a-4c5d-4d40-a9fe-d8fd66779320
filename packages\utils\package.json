{"name": "@repo/utils", "version": "1.0.0", "private": true, "type": "module", "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "types": "./dist/index.d.ts"}, "./alerting": {"import": "./dist/alerting.js", "types": "./dist/alerting.d.ts"}, "./application-service": {"import": "./dist/application-service.js", "types": "./dist/application-service.d.ts"}, "./payment-notifications": {"import": "./dist/payment-notifications.js", "types": "./dist/payment-notifications.d.ts"}, "./bot-detection": {"import": "./dist/bot-detection.js", "types": "./dist/bot-detection.d.ts"}, "./performance": {"import": "./dist/performance.js", "types": "./dist/performance.d.ts"}}, "browser": {"./src/logging.ts": "./src/logging.client.ts"}, "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint --config eslint.config.js src/", "type-check": "tsc --noEmit", "clean": "rd /s /q dist", "test": "jest", "test:performance": "jest --testMatch='**/*-performance.test.ts'"}, "dependencies": {"axios": "^1.6.0", "zod": "^4.0.10", "node-fetch": "^3.3.2", "fetch-blob": "*", "formdata-polyfill": "*", "winston": "^3.11.0", "@sentry/node": "^9.42.1", "@sentry/nextjs": "^9.42.1"}, "devDependencies": {"@types/jest": "^30.0.0", "@types/node-fetch": "^2.6.4", "eslint": "^9.32.0", "jest": "^30.0.5", "ts-jest": "^29.4.0", "typescript": "^5.0.0"}}