import type { Metada<PERSON> } from 'next';
import React from 'react';
import { Inter } from 'next/font/google';
import '@repo/ui/styles/globals.css';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'Admin Panel - Creator Promotion Platform',
  description: 'Admin panel untuk mengelola platform promosi kreator',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="id">
      <body className={inter.className}>{children}</body>
    </html>
  );
}
