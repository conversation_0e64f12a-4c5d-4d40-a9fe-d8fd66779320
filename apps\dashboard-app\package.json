{"name": "@repo/dashboard-app", "version": "1.0.0", "private": true, "type": "module", "scripts": {"build": "next build", "dev": "next dev --port 3002", "lint": "next lint", "start": "next start", "type-check": "tsc --noEmit", "clean": "rd /s /q .next dist", "test": "jest", "test:e2e": "playwright test", "test:performance": "jest --testMatch='**/*-performance.test.ts'", "test:security": "jest --testMatch='**/__tests__/security/*.test.ts'"}, "dependencies": {"@repo/cache": "*", "@repo/config": "*", "@repo/convex": "*", "@repo/database": "*", "@repo/ui": "*", "@repo/utils": "*", "@supabase/supabase-js": "^2.55.0", "@trpc/client": "^11.4.3", "@trpc/react-query": "^11.4.3", "@trpc/server": "^11.4.3", "better-auth": "^1.3.4", "convex": "^1.26.2", "next": "^15.4.4", "react": "^19.1.1", "react-dom": "^19.1.1", "zod": "^4.0.10"}, "devDependencies": {"@playwright/test": "^1.40.0", "@testing-library/jest-dom": "^6.0.0", "@testing-library/react": "^16.3.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "eslint": "^9.32.0", "eslint-config-next": "^15.4.4", "jest": "^30.0.5", "typescript": "^5.0.0", "uuid": "^11.1.0"}}