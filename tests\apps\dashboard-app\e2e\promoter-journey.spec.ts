import { test, expect } from '@playwright/test';
import { v4 as uuidv4 } from 'uuid';

/**
 * End-to-End Test for Promoter Journey
 *
 * This test covers the complete promoter journey:
 * 1. Registration and login
 * 2. Social account connection
 * 3. Campaign discovery and application
 * 4. Content creation and submission
 * 5. Tracking metrics
 * 6. Receiving payment
 */

test.describe('Promoter Journey E2E Test', () => {
  const testEmail = `promoter-${uuidv4().substring(0, 8)}@test.com`;
  const testPassword = 'TestPassword123!';
  let applicationId: string;

  test.beforeAll(async () => {
    // Setup test data if needed
    // This could include creating a test campaign to apply for
  });

  test('Promoter Registration and Login', async ({ page }) => {
    // Visit auth page
    await page.goto('http://auth.localhost:3000/register');

    // Fill registration form
    await page.fill('input[name="name"]', 'Test Promoter');
    await page.fill('input[name="email"]', testEmail);
    await page.fill('input[name="password"]', testPassword);
    await page.fill('input[name="confirmPassword"]', testPassword);

    // Select promoter role
    await page.click('input[value="promoter"]');

    // Submit form
    await page.click('button[type="submit"]');

    // Verify redirect to dashboard
    await page.waitForURL('http://dashboard.localhost:3000/promoter');

    // Verify dashboard loaded correctly
    await expect(page.locator('h1')).toContainText('Promoter Dashboard');
  });

  test('Social Account Connection', async ({ page }) => {
    // Login if needed
    await page.goto('http://auth.localhost:3000/login');
    await page.fill('input[name="email"]', testEmail);
    await page.fill('input[name="password"]', testPassword);
    await page.click('button[type="submit"]');

    // Navigate to account settings
    await page.goto('http://dashboard.localhost:3000/promoter/settings');

    // Note: OAuth flows are difficult to test in E2E
    // For this test, we'll mock the connection or verify the UI elements

    // Verify social connection buttons exist
    await expect(
      page.locator('button:has-text("Connect TikTok")')
    ).toBeVisible();
    await expect(
      page.locator('button:has-text("Connect Instagram")')
    ).toBeVisible();

    // Mock: Simulate successful connection
    // In a real test, we'd need to mock the OAuth provider or use a test account
    console.log(
      'Social account connection would be tested with mock OAuth provider'
    );
  });

  test('Campaign Discovery and Application', async ({ page }) => {
    // Login if needed
    await page.goto('http://auth.localhost:3000/login');
    await page.fill('input[name="email"]', testEmail);
    await page.fill('input[name="password"]', testPassword);
    await page.click('button[type="submit"]');

    // Navigate to available campaigns
    await page.goto('http://dashboard.localhost:3000/promoter/campaigns');

    // Check if campaigns are listed
    const hasCampaigns = (await page.locator('div.campaign-card').count()) > 0;

    if (hasCampaigns) {
      // Click on first campaign
      await page.click('div.campaign-card:first-child a');

      // Apply for campaign
      await page.click('button:has-text("Apply")');

      // Fill application form if needed
      await page.fill(
        'textarea[name="motivation"]',
        'I want to promote this campaign because it aligns with my content.'
      );

      // Submit application
      await page.click('button[type="submit"]');

      // Verify application submitted
      await expect(page.locator('.toast')).toContainText(
        'Application submitted'
      );

      // Get application ID from URL or response for later tests
      const url = page.url();
      if (url.includes('applications/')) {
        applicationId = url.split('/').pop() || '';
      }
    } else {
      console.log('No campaigns available for testing, using mock application');
      applicationId = 'mock-application-id';
    }
  });

  test('Content Creation and Submission', async ({ page }) => {
    // Login if needed
    await page.goto('http://auth.localhost:3000/login');
    await page.fill('input[name="email"]', testEmail);
    await page.fill('input[name="password"]', testPassword);
    await page.click('button[type="submit"]');

    // Navigate to applications
    await page.goto('http://dashboard.localhost:3000/promoter/applications');

    // Find the application (mock if needed)
    const hasApplications = (await page.locator('table tbody tr').count()) > 0;

    if (hasApplications) {
      // Click on the application
      await page.click('table tbody tr:first-child a');
    } else {
      // Mock: Navigate directly to content editor with mock ID
      await page.goto(
        `http://dashboard.localhost:3000/promoter/applications/${applicationId}/content`
      );
    }

    // Use content editor
    await page.fill(
      'textarea[name="content"]',
      'This is my promotional content for the campaign.'
    );

    // Upload content (mock)
    await page.setInputFiles('input[type="file"]', {
      name: 'promo-content.jpg',
      mimeType: 'image/jpeg',
      buffer: Buffer.from('test promo content'),
    });

    // Submit content
    await page.click('button:has-text("Submit Content")');

    // Verify submission
    await expect(page.locator('.toast')).toContainText('Content submitted');
  });

  test('Tracking Metrics', async ({ page }) => {
    // Login if needed
    await page.goto('http://auth.localhost:3000/login');
    await page.fill('input[name="email"]', testEmail);
    await page.fill('input[name="password"]', testPassword);
    await page.click('button[type="submit"]');

    // Navigate to analytics
    await page.goto('http://dashboard.localhost:3000/promoter/analytics');

    // Verify analytics components are loaded
    await expect(
      page.locator('h2:has-text("Performance Metrics")')
    ).toBeVisible();

    // Check metrics cards
    await expect(page.locator('.metrics-card')).toBeVisible();

    // Check campaign performance table
    await expect(page.locator('table')).toBeVisible();
  });

  test('Receiving Payment', async ({ page }) => {
    // Login if needed
    await page.goto('http://auth.localhost:3000/login');
    await page.fill('input[name="email"]', testEmail);
    await page.fill('input[name="password"]', testPassword);
    await page.click('button[type="submit"]');

    // Navigate to earnings
    await page.goto('http://dashboard.localhost:3000/promoter/earnings');

    // Verify earnings page components
    await expect(
      page.locator('h2:has-text("Earnings Overview")')
    ).toBeVisible();

    // Check earnings history
    await expect(page.locator('h3:has-text("Payment History")')).toBeVisible();

    // Check payment method section
    await expect(page.locator('h3:has-text("Payment Methods")')).toBeVisible();
  });

  test.afterAll(async () => {
    // Clean up test data if needed
  });
});
