{"name": "@repo/auth-app", "version": "1.0.0", "private": true, "type": "module", "scripts": {"build": "next build", "dev": "next dev --port 3001", "dev:direct": "next dev --port 3001", "lint": "next lint", "start": "next start", "type-check": "tsc --noEmit", "clean": "rd /s /q .next dist", "test": "jest", "test:security": "jest --testMatch='**/__tests__/security/*.test.ts'", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed"}, "dependencies": {"@repo/auth": "*", "@repo/config": "*", "@repo/database": "*", "@repo/ui": "*", "@supabase/ssr": "^0.6.1", "next": "^15.4.4", "react": "^19.1.1", "react-dom": "^19.1.1", "server-only": "^0.0.1"}, "devDependencies": {"@playwright/test": "^1.45.0", "@testing-library/jest-dom": "^6.0.0", "@testing-library/react": "^16.3.0", "@types/cookie": "^0.6.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "eslint": "^9.32.0", "eslint-config-next": "^15.4.4", "jest": "^30.0.5", "typescript": "^5.0.0"}}