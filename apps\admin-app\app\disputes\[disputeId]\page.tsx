import React from 'react';

// Type definition for dispute data
type DisputeData = {
  id: string;
  creator: string;
  promoter: string;
  reason: string;
  status: string;
  description: string;
  timeline: Array<{ date: string; event: string }>;
};

// Mock data fetching for a single dispute
const getDisputeDetails = async (disputeId: string): Promise<DisputeData> => {
  // In a real app, fetch from your database based on the ID
  console.log(`Fetching details for dispute: ${disputeId}`);
  return {
    id: disputeId,
    creator: 'Creator<PERSON>',
    promoter: 'PromoterX',
    reason: 'Invalid Traffic/Bot Activity',
    status: 'Open',
    description:
      'The creator claims that a significant portion of the views generated by the promoter appear to be from bots, based on low engagement rates and suspicious traffic patterns shown in their analytics.',
    timeline: [
      { date: '2025-08-15', event: 'Dispute opened by CreatorA' },
      { date: '2025-08-16', event: 'Initial evidence submitted by CreatorA' },
    ],
  };
};

// A simple form for admin actions - this would be a client component in a real app
const AdminActionForm = ({ dispute }: { dispute: DisputeData }) => {
  // 'use client'; // This would be a client component
  return (
    <div className="mt-6">
      <h3 className="text-lg font-semibold mb-2">Admin Actions</h3>
      <div className="bg-white p-6 rounded-lg shadow">
        <div className="space-y-4">
          <div>
            <label
              htmlFor="status"
              className="block text-sm font-medium text-gray-700"
            >
              Change Status
            </label>
            <select
              id="status"
              name="status"
              className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
            >
              <option>Open</option>
              <option>Under Review</option>
              <option>Awaiting More Info</option>
              <option>Closed</option>
            </select>
          </div>
          <div>
            <label
              htmlFor="comment"
              className="block text-sm font-medium text-gray-700"
            >
              Add Internal Comment
            </label>
            <textarea
              id="comment"
              name="comment"
              rows={3}
              className="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            ></textarea>
          </div>
          <button className="bg-indigo-600 text-white px-4 py-2 rounded-lg text-sm font-semibold hover:bg-indigo-700">
            Submit Action
          </button>
        </div>
      </div>
    </div>
  );
};

const DisputeDetailPage = async ({
  params,
}: {
  params: Promise<{ disputeId: string }>;
}) => {
  const { disputeId } = await params;
  const dispute = await getDisputeDetails(disputeId);

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-2">Dispute Details</h1>
      <p className="text-sm text-gray-500 mb-4">Ticket ID: {dispute.id}</p>

      <div className="bg-white p-6 rounded-lg shadow">
        <h2 className="text-xl font-semibold mb-4">Summary</h2>
        <p>
          <strong>Reason:</strong> {dispute.reason}
        </p>
        <p>
          <strong>Parties:</strong> {dispute.creator} vs. {dispute.promoter}
        </p>
        <p>
          <strong>Status:</strong>{' '}
          <span className="font-semibold text-red-600">{dispute.status}</span>
        </p>
        <p className="mt-4">
          <strong>Description:</strong>
        </p>
        <p>{dispute.description}</p>
      </div>

      <div className="mt-6">
        <h3 className="text-lg font-semibold mb-2">Timeline</h3>
        <div className="bg-white p-6 rounded-lg shadow">
          <ul>
            {dispute.timeline.map(item => (
              <li
                key={item.date}
                className="border-l-2 pl-4 mb-2 border-indigo-600"
              >
                <p className="font-semibold">{item.date}</p>
                <p className="text-gray-600">{item.event}</p>
              </li>
            ))}
          </ul>
        </div>
      </div>

      <AdminActionForm dispute={dispute} />
    </div>
  );
};

export default DisputeDetailPage;
