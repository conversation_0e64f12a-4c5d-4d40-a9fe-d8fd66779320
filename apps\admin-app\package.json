{"name": "@repo/admin-app", "version": "1.0.0", "private": true, "type": "module", "scripts": {"build": "next build", "dev": "next dev --port 3003", "lint": "next lint", "start": "next start", "type-check": "tsc --noEmit", "clean": "rd /s /q .next dist", "test": "jest", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed"}, "dependencies": {"next": "^15.4.4", "react": "^19.1.1", "react-dom": "^19.1.1", "better-auth": "^1.3.4", "bcryptjs": "^3.0.2", "jsonwebtoken": "^9.0.0", "lucide-react": "^0.532.0", "@repo/ui": "*", "@repo/config": "*", "@repo/database": "*", "@repo/cache": "*", "@repo/utils": "*"}, "devDependencies": {"@playwright/test": "^1.45.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/bcryptjs": "^2.4.2", "@types/jsonwebtoken": "^9.0.2", "typescript": "^5.0.0", "eslint": "^9.32.0", "eslint-config-next": "^15.4.4", "jest": "^30.0.5", "@testing-library/react": "^16.3.0", "@testing-library/jest-dom": "^6.0.0"}}