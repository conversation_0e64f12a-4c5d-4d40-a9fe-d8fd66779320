{"name": "creator-promotion-platform", "private": true, "version": "1.0.0", "description": "Platform promosi konten kreator dengan sistem pay-per-view dan deteksi bot", "scripts": {"build": "node scripts/build-with-timeout.cjs", "dev": "node scripts/dev-with-timeout.cjs", "dev:app": "node scripts/single-app-dev.cjs", "dev:auth": "node scripts/dev-auth-only.cjs", "dev:bg": "node scripts/background-dev.cjs", "test": "vitest", "test:jest": "turbo run test", "test:unit": "vitest **/__tests__/unit/", "test:integration": "vitest **/__tests__/integration/", "test:e2e": "turbo run test -- --testMatch='**/__tests__/e2e/**/*.spec.ts'", "test:e2e:auth": "cd apps/auth-app && npm run test:e2e", "test:e2e:admin": "cd apps/admin-app && npm run test:e2e", "test:e2e:dashboard": "cd apps/dashboard-app && npm run test:e2e", "test:e2e:landing": "cd apps/landing-page && npm run test:e2e", "test:e2e:integration": "playwright test --config=playwright.config.integration.ts", "test:e2e:all": "npm run test:e2e:auth && npm run test:e2e:admin && npm run test:e2e:dashboard && npm run test:e2e:landing", "test:security": "vitest **/__tests__/security/", "test:performance": "vitest **/__tests__/performance/", "test:ui": "vitest --ui", "test:load": "k6 run apps/dashboard-app/__tests__/load/load-test.js", "migrate": "node scripts/migrate-with-timeout.cjs", "lint": "turbo run lint", "type-check": "turbo run type-check", "clean": "turbo run clean && rd /s /q node_modules", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,json,md}\""}, "devDependencies": {"@turbo/gen": "^2.5.5", "@types/node": "^24.1.0", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "@vitejs/plugin-react": "^4.7.0", "@vitest/ui": "^3.2.4", "eslint": "^9.32.0", "jsdom": "^26.1.0", "madge": "^7.0.0", "prettier": "^3.0.0", "turbo": "^2.5.5", "typescript": "^5.0.0", "typescript-eslint": "^8.38.0", "vitest": "^3.2.4"}, "packageManager": "npm@9.0.0", "type": "module", "workspaces": ["apps/*", "packages/*"], "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "convex": "^1.26.2", "lucide-react": "^0.539.0"}}