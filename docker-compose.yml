version: '3.8'

services:
  # Database
  postgres:
    image: postgres:15-alpine
    container_name: creator-platform-db
    environment:
      POSTGRES_DB: creator_platform
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - creator-platform-network

  # Cache
  redis:
    image: redis:7-alpine
    container_name: creator-platform-cache
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - creator-platform-network

  # API Server
  api-server:
    build:
      context: .
      dockerfile: apps/api-server/Dockerfile
    container_name: creator-platform-api
    ports:
      - "3004:3004"
    environment:
      - NODE_ENV=development
      - DATABASE_URL=********************************************/creator_platform
      - REDIS_URL=redis://redis:6379
      - PORT=3004
    depends_on:
      - postgres
      - redis
    networks:
      - creator-platform-network

  # Landing Page
  landing-page:
    build:
      context: .
      dockerfile: apps/landing-page/Dockerfile
    container_name: creator-platform-landing
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - PORT=3000
    networks:
      - creator-platform-network

  # Auth App
  auth-app:
    build:
      context: .
      dockerfile: apps/auth-app/Dockerfile
    container_name: creator-platform-auth
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=development
      - DATABASE_URL=********************************************/creator_platform
      - REDIS_URL=redis://redis:6379
      - PORT=3001
      - NEXTAUTH_SECRET=your-secret-key-here
      - NEXTAUTH_URL=http://localhost:3001
      - TIKTOK_CLIENT_ID=${TIKTOK_CLIENT_ID}
      - TIKTOK_CLIENT_SECRET=${TIKTOK_CLIENT_SECRET}
      - INSTAGRAM_CLIENT_ID=${INSTAGRAM_CLIENT_ID}
      - INSTAGRAM_CLIENT_SECRET=${INSTAGRAM_CLIENT_SECRET}
    depends_on:
      - postgres
      - redis
    networks:
      - creator-platform-network

  # Dashboard App
  dashboard-app:
    build:
      context: .
      dockerfile: apps/dashboard-app/Dockerfile
    container_name: creator-platform-dashboard
    ports:
      - "3002:3002"
    environment:
      - NODE_ENV=development
      - DATABASE_URL=********************************************/creator_platform
      - REDIS_URL=redis://redis:6379
      - PORT=3002
    depends_on:
      - postgres
      - redis
      - api-server
    networks:
      - creator-platform-network

  # Admin App
  admin-app:
    build:
      context: .
      dockerfile: apps/admin-app/Dockerfile
    container_name: creator-platform-admin
    ports:
      - "3003:3003"
    environment:
      - NODE_ENV=development
      - DATABASE_URL=********************************************/creator_platform
      - REDIS_URL=redis://redis:6379
      - PORT=3003
    depends_on:
      - postgres
      - redis
    networks:
      - creator-platform-network

volumes:
  postgres_data:
  redis_data:

networks:
  creator-platform-network:
    driver: bridge