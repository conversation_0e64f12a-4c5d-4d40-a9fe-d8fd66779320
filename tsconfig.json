{"compilerOptions": {"target": "ES2022", "lib": ["dom", "dom.iterable", "ES6"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@repo/ui": ["./packages/ui/src"], "@repo/database/*": ["./packages/database/src/*"], "@repo/cache": ["./packages/cache/src"], "@repo/utils/*": ["./packages/utils/src/*"], "@repo/config": ["./packages/config/src"], "@repo/auth": ["./packages/auth/src"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules", "dist", ".next"]}