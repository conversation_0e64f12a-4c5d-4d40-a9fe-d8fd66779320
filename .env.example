# API Server
PORT=4000

# Supabase
# Replace with your project's URL and anon key
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key

# Database Configuration
DATABASE_URL="postgresql://user:password@localhost:5432/content_boost"

# BetterAuth Configuration
NEXT_PUBLIC_APP_URL="http://localhost:3000"
BETTER_AUTH_SECRET="your-secret-key-here"

# Google OAuth (optional)
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"

# GitHub OAuth (optional)
GITHUB_CLIENT_ID="your-github-client-id"
GITHUB_CLIENT_SECRET="your-github-client-secret"

# Legacy Supabase (for backward compatibility)
NEXT_PUBLIC_SUPABASE_URL="your-supabase-url"
NEXT_PUBLIC_SUPABASE_ANON_KEY="your-supabase-anon-key"

# Redis
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=

# Authentication (Legacy - to be removed)
NEXTAUTH_SECRET=your-nextauth-secret
NEXTAUTH_URL=http://localhost:3000

# Social Media APIs
TIKTOK_CLIENT_ID=your-tiktok-client-id
TIKTOK_CLIENT_SECRET=your-tiktok-client-secret
INSTAGRAM_CLIENT_ID=your-instagram-client-id
INSTAGRAM_CLIENT_SECRET=your-instagram-client-secret

# Platform Settings
PLATFORM_FEE_PERCENTAGE=5
BOT_DETECTION_ENABLED=true

# Monitoring
SENTRY_DSN=
LOG_LEVEL=info