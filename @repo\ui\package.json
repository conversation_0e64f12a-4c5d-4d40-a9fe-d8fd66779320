{"name": "@repo/ui", "version": "1.0.0", "main": "./components/ui/index.ts", "types": "./components/ui/index.ts", "exports": {"./components/ui/*": "./components/ui/*", "./lib/*": "./lib/*"}, "dependencies": {"@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-scroll-area": "^1.2.1", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-textarea": "^1.1.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "tailwind-merge": "^2.5.4", "lucide-react": "^0.454.0", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "typescript": "^5.0.0"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}}